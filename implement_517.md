# Rush Hour Microservice Refactoring - Implementation Plan

## Overview

This document provides a detailed step-by-step implementation plan to refactor the Rush Hour microservice from a functional proof of concept to a production-grade service, aligned with the Synapse ITS Go Microservices Coding Rules and Style Guide.

## Current State Analysis

The Rush Hour microservice currently exists as a functional POC with the following components:
- Socket.IO server with Redis adapter support
- Basic authentication flows for FSA and Gateway clients
- Protobuf-based message handling with envelope structure
- Stream tracking and room management
- Permission checking framework
- Comprehensive test coverage

## Refactoring Objectives

1. **Production-grade Architecture**: Align with established microservice patterns
2. **Enhanced Error Handling**: Implement robust error handling with proper logging
3. **Improved Testing**: Expand test coverage and add integration tests
4. **Performance Optimization**: Optimize Redis usage and connection management
5. **Security Hardening**: Strengthen authentication and authorization flows
6. **Observability**: Add comprehensive monitoring and metrics
7. **Documentation**: Complete API documentation and deployment guides

## Implementation Steps

### Phase 1: Foundation and Standards Alignment (Week 1)

#### Step 1.1: Code Structure Standardization
**Estimated Time**: 2 days
**Files to Modify**:
- `microservices/rushhour/main.go`
- `microservices/rushhour/app/app.go`
- `microservices/rushhour/app/routes.go`

**Tasks**:
1. Ensure `main.go` follows the established pattern with minimal logic
2. Refactor `Run()` function to match dependency injection patterns from other services
3. Standardize error handling with package-level error variables
4. Implement proper graceful shutdown with timeout handling
5. Add configuration validation at startup

**Acceptance Criteria**:
- Main function delegates all logic to testable `Run()` function
- All dependencies injected via function parameters
- Graceful shutdown completes within 30 seconds
- Configuration errors logged with actionable messages

#### Step 1.2: Logging and Error Handling Standardization
**Estimated Time**: 1 day
**Files to Modify**:
- All `.go` files in `microservices/rushhour/`

**Tasks**:
1. Replace ad-hoc error handling with standardized patterns
2. Define package-level error variables using `errors.New()`
3. Implement structured logging with consistent field names
4. Add request correlation IDs for tracing
5. Ensure all errors are wrapped with context using `fmt.Errorf()` and `%w`

**Acceptance Criteria**:
- All errors follow `ErrPackageLevelName` naming convention
- Structured logging includes relevant context (user_id, device_id, org_id)
- Error messages are actionable and include correlation IDs
- Log levels are appropriate (Debug, Info, Warn, Error, Fatal)

#### Step 1.3: Dependency Management and Module Structure
**Estimated Time**: 1 day
**Files to Modify**:
- `microservices/rushhour/go.mod`
- `microservices/rushhour/go.sum`

**Tasks**:
1. Review and update all dependencies to latest stable versions
2. Remove unused dependencies
3. Ensure all shared library versions are consistent
4. Add any missing dependencies for production features
5. Run `go mod tidy` and verify clean build

**Acceptance Criteria**:
- All dependencies are at latest stable versions
- No unused dependencies remain
- Build completes without warnings
- All tests pass with updated dependencies

### Phase 2: Core Service Refactoring (Week 2)

#### Step 2.1: Socket.IO Service Architecture Enhancement
**Estimated Time**: 3 days
**Files to Modify**:
- `microservices/rushhour/modules/socketio/service.go`
- `microservices/rushhour/modules/socketio/handler.go`

**Tasks**:
1. Implement connection pooling for Redis client
2. Add circuit breaker pattern for Redis operations
3. Enhance namespace separation for different client types
4. Implement connection state management with proper cleanup
5. Add comprehensive metrics collection points
6. Optimize room management for better performance

**Acceptance Criteria**:
- Redis connections are pooled and reused efficiently
- Circuit breaker prevents cascade failures
- Namespaces properly isolate FSA and Gateway clients
- Connection cleanup prevents memory leaks
- Metrics are collected for all major operations

#### Step 2.2: Authentication and Authorization Hardening
**Estimated Time**: 2 days
**Files to Modify**:
- `microservices/rushhour/auth/fsa.go`
- `microservices/rushhour/auth/gateway.go`
- `microservices/rushhour/permissions/checker.go`

**Tasks**:
1. Implement JWT token refresh mechanism for FSA clients
2. Add rate limiting for authentication attempts
3. Enhance permission caching with TTL
4. Implement audit logging for authentication events
5. Add support for token revocation
6. Strengthen gateway authentication with additional validation

**Acceptance Criteria**:
- JWT tokens are refreshed automatically before expiration
- Rate limiting prevents brute force attacks
- Permission checks are cached for performance
- All authentication events are audited
- Token revocation is properly handled
- Gateway authentication includes device validation

### Phase 3: Message Processing and Routing (Week 3)

#### Step 3.1: Message Routing Optimization
**Estimated Time**: 2 days
**Files to Modify**:
- `microservices/rushhour/modules/socketio/binary_handler.go`
- `microservices/rushhour/domain/envelope.go`

**Tasks**:
1. Implement message queuing for high-throughput scenarios
2. Add message deduplication to prevent duplicate processing
3. Enhance envelope validation with schema verification
4. Implement message priority handling
5. Add message compression for large payloads
6. Optimize protobuf serialization/deserialization

**Acceptance Criteria**:
- Messages are queued and processed efficiently under load
- Duplicate messages are detected and filtered
- Envelope validation catches malformed messages
- High-priority messages are processed first
- Large messages are compressed automatically
- Protobuf operations are optimized for performance

#### Step 3.2: Stream Management Enhancement
**Estimated Time**: 2 days
**Files to Modify**:
- `microservices/rushhour/tracking/stream_tracker.go`
- `microservices/rushhour/domain/socket_registry.go`

**Tasks**:
1. Implement stream quality monitoring
2. Add automatic stream recovery on connection loss
3. Enhance viewer tracking with detailed analytics
4. Implement stream bandwidth management
5. Add stream recording capabilities for debugging
6. Optimize room cleanup and garbage collection

**Acceptance Criteria**:
- Stream quality is monitored and reported
- Streams automatically recover from interruptions
- Viewer analytics provide detailed insights
- Bandwidth usage is controlled and monitored
- Stream recordings are available for troubleshooting
- Room cleanup prevents resource leaks

#### Step 3.3: Device Communication Protocol
**Estimated Time**: 1 day
**Files to Modify**:
- `microservices/rushhour/modules/socketio/handler.go`

**Tasks**:
1. Implement command acknowledgment system
2. Add command timeout and retry logic
3. Enhance device status tracking
4. Implement command queuing for offline devices
5. Add command history and audit trail
6. Optimize direct messaging performance

**Acceptance Criteria**:
- Commands are acknowledged by target devices
- Failed commands are retried with exponential backoff
- Device status is accurately tracked and reported
- Commands are queued when devices are offline
- Command history is maintained for audit purposes
- Direct messaging achieves sub-100ms latency

### Phase 4: Testing and Quality Assurance (Week 4)

#### Step 4.1: Unit Test Enhancement
**Estimated Time**: 2 days
**Files to Create/Modify**:
- All `*_test.go` files in `microservices/rushhour/`

**Tasks**:
1. Achieve 90%+ code coverage across all packages
2. Add comprehensive mock implementations
3. Implement table-driven tests for complex scenarios
4. Add property-based testing for critical functions
5. Enhance test isolation and cleanup
6. Add performance benchmarks

**Acceptance Criteria**:
- Code coverage exceeds 90% for all packages
- All external dependencies are properly mocked
- Tests cover both happy path and error scenarios
- Property-based tests validate invariants
- Tests run in isolation without side effects
- Benchmarks establish performance baselines

#### Step 4.2: Integration Testing
**Estimated Time**: 2 days
**Files to Create**:
- `microservices/testing/integration/rushhour/`

**Tasks**:
1. Create end-to-end test scenarios
2. Implement multi-instance testing with Redis
3. Add load testing for concurrent connections
4. Test failover and recovery scenarios
5. Validate cross-service communication
6. Add chaos engineering tests

**Acceptance Criteria**:
- E2E tests cover all major user journeys
- Multi-instance scenarios work correctly
- Service handles expected load without degradation
- Failover scenarios complete successfully
- Cross-service communication is reliable
- Chaos tests validate system resilience

### Phase 5: Production Readiness (Week 5)

#### Step 5.1: Monitoring and Observability
**Estimated Time**: 2 days
**Files to Create/Modify**:
- `microservices/rushhour/monitoring/`
- `microservices/rushhour/app/app.go`

**Tasks**:
1. Implement comprehensive metrics collection
2. Add distributed tracing support
3. Create health check endpoints
4. Implement alerting rules
5. Add performance monitoring
6. Create operational dashboards

**Acceptance Criteria**:
- Metrics cover all critical operations
- Traces provide end-to-end visibility
- Health checks accurately reflect service state
- Alerts fire for critical conditions
- Performance metrics are collected and analyzed
- Dashboards provide operational insights

#### Step 5.2: Security Hardening
**Estimated Time**: 1 day
**Files to Modify**:
- All authentication and authorization related files

**Tasks**:
1. Implement security headers and CORS policies
2. Add input validation and sanitization
3. Implement rate limiting and DDoS protection
4. Add security audit logging
5. Perform security vulnerability scanning
6. Implement secrets management

**Acceptance Criteria**:
- Security headers are properly configured
- All inputs are validated and sanitized
- Rate limiting prevents abuse
- Security events are logged and monitored
- No high-severity vulnerabilities exist
- Secrets are managed securely

#### Step 5.3: Documentation and Deployment
**Estimated Time**: 2 days
**Files to Create/Modify**:
- `microservices/rushhour/docs/`
- `microservices/rushhour/README.md`

**Tasks**:
1. Complete API documentation
2. Create deployment guides
3. Document configuration options
4. Create troubleshooting guides
5. Add performance tuning recommendations
6. Create disaster recovery procedures

**Acceptance Criteria**:
- API documentation is complete and accurate
- Deployment procedures are documented and tested
- Configuration options are clearly explained
- Troubleshooting guides cover common issues
- Performance tuning guidelines are provided
- Disaster recovery procedures are validated

## Risk Mitigation

### Technical Risks
1. **Redis Dependency**: Implement fallback mechanisms for Redis unavailability
2. **Performance Degradation**: Establish performance baselines and monitoring
3. **Security Vulnerabilities**: Regular security audits and dependency updates
4. **Data Loss**: Implement proper backup and recovery procedures

### Operational Risks
1. **Deployment Issues**: Comprehensive testing in staging environment
2. **Configuration Errors**: Validation and documentation of all settings
3. **Monitoring Gaps**: Comprehensive observability implementation
4. **Team Knowledge**: Documentation and knowledge transfer sessions

## Success Metrics

1. **Performance**: Sub-100ms message latency, 99.9% uptime
2. **Quality**: 90%+ test coverage, zero critical security vulnerabilities
3. **Reliability**: Automatic recovery from failures, graceful degradation
4. **Maintainability**: Clear documentation, standardized code patterns
5. **Scalability**: Support for 10,000+ concurrent connections

## Environment Configuration

### Required Environment Variables
```bash
# Core Service Configuration
HEALTH_PORT=":8081"                    # Health check endpoint port
LOG_LEVEL="info"                       # Logging level (debug, info, warn, error)
RH_REDIS="redis:6379"                  # Redis connection string
RH_REDIS_PASSWORD=""                   # Redis password (if required)
RH_REDIS_DB="0"                        # Redis database number

# Database Configuration
POSTGRES_CONNECTION_STRING=""           # PostgreSQL connection string
POSTGRES_MAX_CONNECTIONS="25"          # Maximum database connections
POSTGRES_CONNECTION_TIMEOUT="30s"      # Connection timeout

# Authentication Configuration
JWT_SECRET_KEY=""                      # JWT signing key
JWT_EXPIRATION_TIME="24h"              # JWT token expiration
GATEWAY_AUTH_TIMEOUT="30s"             # Gateway authentication timeout

# Performance Configuration
MAX_CONCURRENT_CONNECTIONS="10000"     # Maximum concurrent Socket.IO connections
MESSAGE_QUEUE_SIZE="1000"              # Message queue buffer size
STREAM_BUFFER_SIZE="100"               # Stream message buffer size
CONNECTION_TIMEOUT="60s"               # Socket.IO connection timeout

# Monitoring Configuration
METRICS_PORT=":9090"                   # Prometheus metrics port
TRACING_ENDPOINT=""                    # Jaeger tracing endpoint
ENABLE_PROFILING="false"               # Enable Go profiling endpoints
```

### Docker Configuration
```dockerfile
# Production Dockerfile optimizations
FROM golang:1.24.3-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o rushhour .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/rushhour .
EXPOSE 8080 8081 9090
CMD ["./rushhour"]
```

## Detailed Implementation Tasks

### Phase 1 Detailed Tasks

#### Task 1.1.1: Main Function Refactoring
**File**: `microservices/rushhour/main.go`
**Estimated Time**: 4 hours

```go
// Expected structure after refactoring
func main() {
    Run(
        context.Background(),
        os.Getenv("HEALTH_PORT"),
        ":8080",
        connect.NewConnections,
        bqbatch.New,
        DefaultServer,
        DefaultSignalChan,
        NewRedisClient,
        NewMetricsCollector,
    )
}

func Run(
    ctx context.Context,
    healthPort string,
    addr string,
    newConns func(context.Context) *connect.Connections,
    newBatch func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher,
    newServer func(string, http.Handler) Server,
    newSignals func() <-chan os.Signal,
    newRedis func(context.Context) RedisClient,
    newMetrics func() MetricsCollector,
) error
```

**Validation Steps**:
1. Verify dependency injection works correctly
2. Test graceful shutdown scenarios
3. Validate configuration loading
4. Ensure proper error propagation

#### Task 1.1.2: Error Handling Standardization
**Files**: All Go files in the project
**Estimated Time**: 6 hours

```go
// Package-level error definitions
var (
    ErrInvalidAuthentication = errors.New("invalid authentication credentials")
    ErrDeviceNotFound       = errors.New("device not found or not accessible")
    ErrConnectionTimeout    = errors.New("connection timeout exceeded")
    ErrRedisUnavailable     = errors.New("redis service unavailable")
    ErrPermissionDenied     = errors.New("permission denied for requested operation")
    ErrInvalidMessage       = errors.New("invalid message format or content")
    ErrStreamNotFound       = errors.New("stream not found or not active")
    ErrGatewayOffline       = errors.New("target gateway is offline")
)

// Error wrapping example
func (s *Service) authenticateUser(token string) (*domain.AuthInfo, error) {
    authInfo, err := s.validateJWT(token)
    if err != nil {
        logger.Warnf("JWT validation failed: %v", err)
        return nil, fmt.Errorf("%w: %v", ErrInvalidAuthentication, err)
    }
    return authInfo, nil
}
```

**Validation Steps**:
1. All errors use package-level variables
2. Error messages include actionable information
3. Errors are properly wrapped with context
4. Logging levels are appropriate for each error type

### Phase 2 Detailed Tasks

#### Task 2.1.1: Redis Connection Management
**File**: `microservices/rushhour/modules/socketio/service.go`
**Estimated Time**: 8 hours

```go
// Enhanced Redis client with connection pooling
type RedisManager struct {
    client      *redis.Client
    circuitBreaker *CircuitBreaker
    metrics     MetricsCollector
    ctx         context.Context
}

func NewRedisManager(ctx context.Context, addr string) (*RedisManager, error) {
    client := redis.NewClient(&redis.Options{
        Addr:         addr,
        PoolSize:     20,
        MinIdleConns: 5,
        MaxRetries:   3,
        DialTimeout:  5 * time.Second,
        ReadTimeout:  3 * time.Second,
        WriteTimeout: 3 * time.Second,
    })

    // Test connection
    if err := client.Ping(ctx).Err(); err != nil {
        return nil, fmt.Errorf("%w: %v", ErrRedisUnavailable, err)
    }

    return &RedisManager{
        client:         client,
        circuitBreaker: NewCircuitBreaker(),
        metrics:        NewMetricsCollector(),
        ctx:           ctx,
    }, nil
}
```

**Validation Steps**:
1. Connection pooling works under load
2. Circuit breaker prevents cascade failures
3. Metrics are collected for all operations
4. Graceful degradation when Redis is unavailable

#### Task 2.1.2: Namespace Implementation
**File**: `microservices/rushhour/modules/socketio/handler.go`
**Estimated Time**: 6 hours

```go
// Namespace-specific handlers
func (h *Handler) SetupNamespaces() {
    // FSA namespace
    fsaNamespace := h.server.Of("/auth/fsa", nil)
    fsaNamespace.On("connection", h.handleFSAConnection)

    // Gateway namespace
    gatewayNamespace := h.server.Of("/auth/gateway", nil)
    gatewayNamespace.On("connection", h.handleGatewayConnection)

    // Admin namespace (for monitoring)
    adminNamespace := h.server.Of("/admin", nil)
    adminNamespace.Use(h.adminAuthMiddleware)
    adminNamespace.On("connection", h.handleAdminConnection)
}
```

**Validation Steps**:
1. Namespaces properly isolate client types
2. Authentication is enforced per namespace
3. Cross-namespace communication works correctly
4. Admin namespace provides monitoring capabilities

### Phase 3 Detailed Tasks

#### Task 3.1.1: Message Queue Implementation
**File**: `microservices/rushhour/modules/socketio/message_queue.go`
**Estimated Time**: 10 hours

```go
// Message queue for high-throughput scenarios
type MessageQueue struct {
    inbound     chan *domain.SocketEnvelope
    outbound    chan *domain.SocketEnvelope
    deadLetter  chan *domain.SocketEnvelope
    workers     int
    metrics     MetricsCollector
    ctx         context.Context
    cancel      context.CancelFunc
}

func NewMessageQueue(ctx context.Context, workers int) *MessageQueue {
    ctx, cancel := context.WithCancel(ctx)

    mq := &MessageQueue{
        inbound:    make(chan *domain.SocketEnvelope, 1000),
        outbound:   make(chan *domain.SocketEnvelope, 1000),
        deadLetter: make(chan *domain.SocketEnvelope, 100),
        workers:    workers,
        metrics:    NewMetricsCollector(),
        ctx:        ctx,
        cancel:     cancel,
    }

    // Start worker goroutines
    for i := 0; i < workers; i++ {
        go mq.worker(i)
    }

    return mq
}
```

**Validation Steps**:
1. Queue handles high message throughput
2. Dead letter queue captures failed messages
3. Worker pool scales appropriately
4. Metrics track queue performance

### Phase 4 Detailed Tasks

#### Task 4.1.1: Comprehensive Test Suite
**Files**: All `*_test.go` files
**Estimated Time**: 16 hours

```go
// Example table-driven test
func TestService_ProcessDeviceMessage(t *testing.T) {
    tests := []struct {
        name           string
        envelope       *domain.SocketEnvelope
        authInfo       *domain.AuthInfo
        expectedError  error
        expectedEvents []string
        setupMocks     func(*mocks.MockDB, *mocks.MockRedis)
    }{
        {
            name: "valid_device_message_with_session_id",
            envelope: &domain.SocketEnvelope{
                DeviceId:  "device123",
                SessionId: "session456",
                Payload:   []byte("test payload"),
            },
            authInfo: &domain.AuthInfo{
                ClientType: domain.ClientTypeGateway,
                GatewayID:  "gateway789",
            },
            expectedError:  nil,
            expectedEvents: []string{"device_message"},
            setupMocks: func(db *mocks.MockDB, redis *mocks.MockRedis) {
                db.On("QueryRow", mock.Anything, "device123").Return(
                    map[string]interface{}{"gateway_id": "gateway789"}, nil)
                redis.On("Get", "rushhour:session:session456").Return(
                    "socket_id_123", nil)
            },
        },
        // Additional test cases...
    }

    for _, tc := range tests {
        tc := tc
        t.Run(tc.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

**Validation Steps**:
1. All major code paths are tested
2. Error scenarios are covered
3. Mocks are properly configured
4. Tests run in isolation

### Phase 5 Detailed Tasks

#### Task 5.1.1: Metrics and Monitoring
**File**: `microservices/rushhour/monitoring/metrics.go`
**Estimated Time**: 8 hours

```go
// Comprehensive metrics collection
type MetricsCollector struct {
    // Connection metrics
    ActiveConnections    prometheus.Gauge
    TotalConnections     prometheus.Counter
    ConnectionDuration   prometheus.Histogram

    // Message metrics
    MessagesProcessed    prometheus.Counter
    MessageLatency       prometheus.Histogram
    MessageErrors        prometheus.Counter

    // Stream metrics
    ActiveStreams        prometheus.Gauge
    StreamViewers        prometheus.Gauge
    StreamBandwidth      prometheus.Gauge

    // Redis metrics
    RedisOperations      prometheus.Counter
    RedisLatency         prometheus.Histogram
    RedisErrors          prometheus.Counter
}
```

**Validation Steps**:
1. All critical metrics are collected
2. Metrics are properly labeled
3. Dashboards display meaningful data
4. Alerts fire for critical conditions

## Testing Strategy

### Unit Testing
- **Target Coverage**: 90%+
- **Focus Areas**: Business logic, error handling, edge cases
- **Tools**: Go testing package, testify, gomock
- **Execution**: Automated in CI/CD pipeline

### Integration Testing
- **Scope**: Multi-service interactions, database operations, Redis operations
- **Environment**: Docker Compose with real dependencies
- **Scenarios**: Authentication flows, message routing, stream management
- **Execution**: Nightly automated runs

### Load Testing
- **Tool**: Artillery.io or k6
- **Scenarios**:
  - 10,000 concurrent connections
  - 1,000 messages per second
  - Stream viewer scaling
- **Metrics**: Latency, throughput, error rate, resource usage

### Security Testing
- **Tools**: OWASP ZAP, gosec, nancy
- **Focus**: Authentication bypass, injection attacks, dependency vulnerabilities
- **Frequency**: Weekly automated scans

## Deployment Strategy

### Staging Deployment
1. Deploy to staging environment with production-like configuration
2. Run comprehensive test suite
3. Perform load testing
4. Validate monitoring and alerting
5. Conduct security scan

### Production Deployment
1. **Blue-Green Deployment**: Zero-downtime deployment strategy
2. **Canary Release**: Gradual rollout to subset of users
3. **Rollback Plan**: Automated rollback on critical metrics
4. **Health Checks**: Comprehensive health validation
5. **Monitoring**: Real-time monitoring during deployment

### Post-Deployment Validation
1. Verify all health checks pass
2. Confirm metrics are within expected ranges
3. Test critical user journeys
4. Monitor error rates and performance
5. Validate alerting systems

## Maintenance and Operations

### Monitoring Checklist
- [ ] Service health endpoints responding
- [ ] Redis connectivity and performance
- [ ] Database connection pool status
- [ ] Message queue depth and processing rate
- [ ] Authentication success/failure rates
- [ ] Stream quality and viewer counts
- [ ] Error rates and types
- [ ] Resource utilization (CPU, memory, network)

### Troubleshooting Guides
1. **Connection Issues**: Check Redis, database, and network connectivity
2. **Authentication Failures**: Verify JWT configuration and database permissions
3. **Performance Degradation**: Monitor queue depths and resource usage
4. **Stream Quality Issues**: Check network bandwidth and device connectivity
5. **Memory Leaks**: Monitor connection cleanup and garbage collection

### Backup and Recovery
1. **Configuration Backup**: Version-controlled configuration files
2. **Database Backup**: Regular PostgreSQL backups
3. **Redis Backup**: Periodic Redis snapshots for session recovery
4. **Disaster Recovery**: Multi-region deployment capability
5. **Recovery Testing**: Monthly disaster recovery drills

## Conclusion

This comprehensive implementation plan provides a structured approach to refactoring the Rush Hour microservice from a POC to a production-ready system. The detailed tasks, validation steps, and operational procedures ensure a high-quality, maintainable, and scalable service that aligns with established coding standards and best practices.

The phased approach allows for incremental improvements while maintaining service availability, and the comprehensive testing strategy ensures quality at each step. The monitoring and operational procedures provide the foundation for reliable production operations.

/workspace/microservices/broker/api/brokerShared/shared.go                                         : 100.00% (30/30 statements)
/workspace/microservices/broker/api/handlers/v3/data/device/handler.go                             : 100.00% (159/159 statements)
/workspace/microservices/broker/api/handlers/v3/data/device/helper.go                              : 100.00% (19/19 statements)
/workspace/microservices/broker/api/handlers/v3/data/fault/handler.go                              : 100.00% (84/84 statements)
/workspace/microservices/broker/api/handlers/v3/data/fault/helper.go                               : 100.00% (102/102 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/authenticate/handler.go                    : 100.00% (86/86 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/authenticate/security.go                   : 100.00% (22/22 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/ingest/handler.go                          : 100.00% (110/110 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/ingest/schemas.go                          : 100.00% (2/2 statements)
/workspace/microservices/broker/api/handlers/v3/gateway/update/handler.go                          : 100.00% (79/79 statements)
/workspace/microservices/broker/api/handlers/v3/user/account/close/handler.go                      : 100.00% (40/40 statements)
/workspace/microservices/broker/api/handlers/v3/user/account/notifications/handler.go              : 100.00% (30/30 statements)
/workspace/microservices/broker/api/handlers/v3/user/account/notifications/repository.go           : 100.00% (10/10 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/handler.go                       : 100.00% (7/7 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/mocks.go                         : 100.00% (17/17 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/shared.go                        : 100.00% (52/52 statements)
/workspace/microservices/broker/api/handlers/v3/user/authenticate/useraccess.go                    : 100.00% (57/57 statements)
/workspace/microservices/broker/api/handlers/v3/user/instruction/handler.go                        : 100.00% (54/54 statements)
/workspace/microservices/broker/api/handlers/v3/user/profile/handler.go                            : 100.00% (34/34 statements)
/workspace/microservices/broker/api/routes/routes.go                                               : 100.00% (26/26 statements)
/workspace/microservices/broker/api/synapse/purge_expired.go                                       : 100.00% (16/16 statements)
/workspace/microservices/broker/api/synapse/route.go                                               : 100.00% (7/7 statements)
/workspace/microservices/broker/main.go                                                            : 97.44% (38/39 statements)
/workspace/microservices/coordinator/internal/setup/bigquery.go                                    : 25.00% (5/20 statements)
/workspace/microservices/coordinator/internal/setup/postgres.go                                    : 100.00% (3/3 statements)
/workspace/microservices/coordinator/internal/setup/pubsub.go                                      : 100.00% (28/28 statements)
/workspace/microservices/coordinator/internal/setup/redis.go                                       : 100.00% (24/24 statements)
/workspace/microservices/coordinator/main.go                                                       : 97.92% (47/48 statements)
/workspace/microservices/etl/main.go                                                               : 98.11% (52/53 statements)
/workspace/microservices/etl/processors/handlers/dlq/batch/handler.go                              : 100.00% (55/55 statements)
/workspace/microservices/etl/processors/handlers/dlq/messages/handler.go                           : 100.00% (30/30 statements)
/workspace/microservices/etl/processors/handlers/etlShared/shared.go                               : 100.00% (46/46 statements)
/workspace/microservices/etl/processors/handlers/gateway/faultLogs/handler.go                      : 100.00% (106/106 statements)
/workspace/microservices/etl/processors/handlers/gateway/faultNotification/handler.go              : 100.00% (88/88 statements)
/workspace/microservices/etl/processors/handlers/gateway/faultNotification/notification_service.go : 100.00% (66/66 statements)
/workspace/microservices/etl/processors/handlers/gateway/gatewayLog/handler.go                     : 100.00% (62/62 statements)
/workspace/microservices/etl/processors/handlers/gateway/helper/helper.go                          : 100.00% (22/22 statements)
/workspace/microservices/etl/processors/handlers/gateway/macAddress/handler.go                     : 100.00% (78/78 statements)
/workspace/microservices/etl/processors/handlers/gateway/monitorName/handler.go                    : 100.00% (82/82 statements)
/workspace/microservices/etl/processors/handlers/gateway/perfStats/handler.go                      : 100.00% (50/50 statements)
/workspace/microservices/etl/processors/handlers/gateway/rmsData/handler.go                        : 100.00% (57/57 statements)
/workspace/microservices/etl/processors/handlers/gateway/rmsEngine/handler.go                      : 100.00% (82/82 statements)
/workspace/microservices/etl/processors/handlers/notifications/handler.go                          : 100.00% (110/110 statements)
/workspace/microservices/etl/processors/handlers/notifications/mailgun/client.go                   : 100.00% (37/37 statements)
/workspace/microservices/etl/processors/handlers/notifications/mailgun/service.go                  : 100.00% (26/26 statements)
/workspace/microservices/etl/processors/handlers/notifications/service.go                          : 100.00% (7/7 statements)
/workspace/microservices/etl/processors/handlers/notifications/twilio/client.go                    : 100.00% (33/33 statements)
/workspace/microservices/etl/processors/handlers/notifications/twilio/service.go                   : 100.00% (36/36 statements)
/workspace/microservices/etl/processors/handlers/raw/handler.go                                    : 100.00% (31/31 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/cache_repository.go          : 100.00% (40/40 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/handler.go                   : 100.00% (16/16 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/persistence_repository.go    : 100.00% (37/37 statements)
/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/service.go                   : 100.00% (121/121 statements)
/workspace/microservices/etl/processors/subscriptions/manager.go                                   : 100.00% (65/65 statements)
/workspace/microservices/etl/processors/subscriptions/schema.go                                    : 100.00% (3/3 statements)
/workspace/microservices/onramp/app/app.go                                                         : 65.00% (26/40 statements)
/workspace/microservices/onramp/app/routes.go                                                      : 84.62% (22/26 statements)
/workspace/microservices/onramp/domain/token.go                                                    : 0.00% (0/3 statements)
/workspace/microservices/onramp/handlers/configGateway.go                                          : 100.00% (3/3 statements)
/workspace/microservices/onramp/handlers/devices.go                                                : 100.00% (3/3 statements)
/workspace/microservices/onramp/handlers/gateway.go                                                : 100.00% (3/3 statements)
/workspace/microservices/onramp/main.go                                                            : 100.00% (40/40 statements)
/workspace/microservices/onramp/middlewares/authentication.go                                      : 71.88% (23/32 statements)
/workspace/microservices/onramp/middlewares/session.go                                             : 100.00% (12/12 statements)
/workspace/microservices/onramp/mock/mock_auth_repository.go                                       : 100.00% (18/18 statements)
/workspace/microservices/onramp/mock/mock_auth_service.go                                          : 100.00% (11/11 statements)
/workspace/microservices/onramp/mock/mock_password_hasher.go                                       : 100.00% (4/4 statements)
/workspace/microservices/onramp/mock/mock_session_store.go                                         : 100.00% (5/5 statements)
/workspace/microservices/onramp/mock/mock_token_generator.go                                       : 100.00% (2/2 statements)
/workspace/microservices/onramp/modules/auth/handler.go                                            : 100.00% (108/108 statements)
/workspace/microservices/onramp/modules/auth/service.go                                            : 100.00% (76/76 statements)
/workspace/microservices/onramp/modules/auth/session/redis_session_store.go                        : 100.00% (26/26 statements)
/workspace/microservices/onramp/modules/auth/storage.go                                            : 100.00% (85/85 statements)
/workspace/microservices/onramp/modules/organization/handler.go                                    : 100.00% (17/17 statements)
/workspace/microservices/onramp/modules/softwaregateway/handler.go                                 : 100.00% (8/8 statements)
/workspace/microservices/onramp/modules/user/handler.go                                            : 100.00% (7/7 statements)
/workspace/microservices/onramp/modules/user/permissions/handler.go                                : 100.00% (32/32 statements)
/workspace/microservices/onramp/pkg/password_hasher.go                                             : 100.00% (3/3 statements)
/workspace/microservices/onramp/pkg/token_generator.go                                             : 100.00% (2/2 statements)
/workspace/microservices/rushhour/app/app.go                                                       : 87.50% (14/16 statements)
/workspace/microservices/rushhour/app/routes.go                                                    : 100.00% (15/15 statements)
/workspace/microservices/rushhour/auth/fsa.go                                                      : 100.00% (26/26 statements)
/workspace/microservices/rushhour/auth/gateway.go                                                  : 100.00% (24/24 statements)
/workspace/microservices/rushhour/domain/auth.go                                                   : 100.00% (19/19 statements)
/workspace/microservices/rushhour/domain/socket_registry.go                                        : 100.00% (44/44 statements)
/workspace/microservices/rushhour/main.go                                                          : 88.37% (38/43 statements)
/workspace/microservices/rushhour/modules/socketio/binary_handler.go                               : 88.89% (72/81 statements)
/workspace/microservices/rushhour/modules/socketio/handler.go                                      : 11.11% (18/162 statements)
/workspace/microservices/rushhour/modules/socketio/service.go                                      : 5.33% (22/413 statements)
/workspace/microservices/rushhour/permissions/checker.go                                           : 100.00% (68/68 statements)
/workspace/microservices/rushhour/tracking/stream_tracker.go                                       : 45.86% (72/157 statements)
/workspace/microservices/testing/utils/utils.go                                                    : 77.78% (77/99 statements)
/workspace/shared/api/authorizer/authorizer.go                                                     : 71.64% (48/67 statements)
/workspace/shared/api/authorizer/device_access.go                                                  : 100.00% (105/105 statements)
/workspace/shared/api/authorizer/device_queries.go                                                 : 100.00% (16/16 statements)
/workspace/shared/api/authorizer/permissions.go                                                    : 100.00% (43/43 statements)
/workspace/shared/api/authorizer/user_queries.go                                                   : 100.00% (27/27 statements)
/workspace/shared/api/handlers/defaultapi/handler.go                                               : 100.00% (1/1 statements)
/workspace/shared/api/helper/helper.go                                                             : 95.45% (21/22 statements)
/workspace/shared/api/jwttokens/general.go                                                         : 100.00% (8/8 statements)
/workspace/shared/api/jwttokens/generate.go                                                        : 100.00% (17/17 statements)
/workspace/shared/api/jwttokens/jwttokens.go                                                       : 72.62% (61/84 statements)
/workspace/shared/api/middleware/bqbatch.go                                                        : 100.00% (4/4 statements)
/workspace/shared/api/middleware/connections.go                                                    : 100.00% (4/4 statements)
/workspace/shared/api/middleware/jwtauthorizer.go                                                  : 100.00% (12/12 statements)
/workspace/shared/api/middleware/synapse_auth.go                                                   : 100.00% (7/7 statements)
/workspace/shared/api/response/response.go                                                         : 100.00% (31/31 statements)
/workspace/shared/api/security/certificates.go                                                     : 100.00% (17/17 statements)
/workspace/shared/api/security/firebase.go                                                         : 100.00% (23/23 statements)
/workspace/shared/api/security/hash.go                                                             : 100.00% (4/4 statements)
/workspace/shared/api/security/secrets.go                                                          : 100.00% (7/7 statements)
/workspace/shared/bqbatch/bqbatch.go                                                               : 74.00% (259/350 statements)
/workspace/shared/bqbatch/helper.go                                                                : 100.00% (51/51 statements)
/workspace/shared/connect/bigquery.go                                                              : 99.55% (220/221 statements)
/workspace/shared/connect/connect.go                                                               : 91.30% (126/138 statements)
/workspace/shared/connect/firestore.go                                                             : 97.62% (41/42 statements)
/workspace/shared/connect/getReleaseIdentifier.go                                                  : 100.00% (13/13 statements)
/workspace/shared/connect/postgres.go                                                              : 96.89% (187/193 statements)
/workspace/shared/connect/pubsub.go                                                                : 100.00% (125/125 statements)
/workspace/shared/connect/redis.go                                                                 : 100.00% (48/48 statements)
/workspace/shared/devices/devices.go                                                               : 100.00% (101/101 statements)
/workspace/shared/devices/edi/edicmu2212/edicmu2212.go                                             : 100.00% (408/408 statements)
/workspace/shared/devices/edi/edicmu2212/logFaultSignalSequence.go                                 : 100.00% (53/53 statements)
/workspace/shared/devices/edi/edicmu2212/logPreviousFail.go                                        : 100.00% (149/149 statements)
/workspace/shared/devices/edi/edicmu2212/utilities.go                                              : 100.00% (5/5 statements)
/workspace/shared/devices/edi/ediecl2010/logACLineEvent.go                                         : 100.00% (27/27 statements)
/workspace/shared/devices/edi/ediecl2010/logConfiguration.go                                       : 100.00% (134/134 statements)
/workspace/shared/devices/edi/ediecl2010/logFaultSignalSequence.go                                 : 100.00% (53/53 statements)
/workspace/shared/devices/edi/ediecl2010/logMonitorReset.go                                        : 100.00% (19/19 statements)
/workspace/shared/devices/edi/ediecl2010/logPreviousFail.go                                        : 100.00% (162/162 statements)
/workspace/shared/devices/edi/ediecl2010/monitorIDandName.go                                       : 100.00% (18/18 statements)
/workspace/shared/devices/edi/ediecl2010/rmsEngineData.go                                          : 100.00% (17/17 statements)
/workspace/shared/devices/edi/ediecl2010/rmsStatus.go                                              : 100.00% (74/74 statements)
/workspace/shared/devices/edi/ediecl2010/utilities.go                                              : 100.00% (15/15 statements)
/workspace/shared/devices/edi/ediecl2018/ediecl2018.go                                             : 100.00% (35/35 statements)
/workspace/shared/devices/edi/edimmu16le/edimmu16le.go                                             : 100.00% (72/72 statements)
/workspace/shared/devices/edi/edimmu16le/logACLineEvent.go                                         : 100.00% (27/27 statements)
/workspace/shared/devices/edi/edimmu16le/logConfiguration.go                                       : 100.00% (283/283 statements)
/workspace/shared/devices/edi/edimmu16le/logFaultSignalSequence.go                                 : 100.00% (32/32 statements)
/workspace/shared/devices/edi/edimmu16le/logMonitorReset.go                                        : 100.00% (27/27 statements)
/workspace/shared/devices/edi/edimmu16le/logPreviousFail.go                                        : 100.00% (149/149 statements)
/workspace/shared/devices/edi/edimmu16le/monitorIDandName.go                                       : 100.00% (8/8 statements)
/workspace/shared/devices/edi/edimmu16le/rmsEngineData.go                                          : 100.00% (13/13 statements)
/workspace/shared/devices/edi/edimmu16le/rmsStatus.go                                              : 100.00% (40/40 statements)
/workspace/shared/devices/edi/helper/bq_converter.go                                               : 100.00% (78/78 statements)
/workspace/shared/devices/edi/helper/helper.go                                                     : 100.00% (479/479 statements)
/workspace/shared/devices/edi/helper/schemas.go                                                    : 100.00% (41/41 statements)
/workspace/shared/devices/helper/helper.go                                                         : 100.00% (25/25 statements)
/workspace/shared/healthz/healthz.go                                                               : 100.00% (42/42 statements)
/workspace/shared/httplogger/httplogger.go                                                         : 100.00% (14/14 statements)
/workspace/shared/logger/logger.go                                                                 : 100.00% (73/73 statements)
/workspace/shared/mocks/bqbatcher/bqbatcher.go                                                     : 100.00% (19/19 statements)
/workspace/shared/mocks/bqexecutor/bigQueryExecutor.go                                             : 68.75% (22/32 statements)
/workspace/shared/mocks/dbexecutor/dbExecutor.go                                                   : 100.00% (50/50 statements)
/workspace/shared/mocks/firestore/firestore.go                                                     : 100.00% (80/80 statements)
/workspace/shared/mocks/healthz/healthz.go                                                         : 100.00% (9/9 statements)
/workspace/shared/mocks/mock.go                                                                    : 100.00% (3/3 statements)
/workspace/shared/mocks/notifications/notifications.go                                             : 100.00% (8/8 statements)
/workspace/shared/mocks/pubsub/pubsub.go                                                           : 100.00% (52/52 statements)
/workspace/shared/mocks/schemaexecutor/schemaMigrationExecutor.go                                  : 100.00% (34/34 statements)
/workspace/shared/pubsubdata/helper.go                                                             : 100.00% (27/27 statements)
/workspace/shared/rest/onramp/helper/helper.go                                                     : 100.00% (32/32 statements)
/workspace/shared/rest/onramp/invites/email.go                                                     : 100.00% (20/20 statements)
/workspace/shared/rest/onramp/invites/invites.go                                                   : 100.00% (568/568 statements)
/workspace/shared/rest/onramp/organization/organization.go                                         : 100.00% (228/228 statements)
/workspace/shared/rest/onramp/organization/schemas.go                                              : 100.00% (1/1 statements)
/workspace/shared/rest/onramp/permissions/permissions.go                                           : 100.00% (155/155 statements)
/workspace/shared/rest/onramp/roles/roles.go                                                       : 100.00% (205/205 statements)
/workspace/shared/rest/onramp/softwaregateway/config/schemas.go                                    : 100.00% (1/1 statements)
/workspace/shared/rest/onramp/softwaregateway/config/softwaregatewayconfig.go                      : 100.00% (87/87 statements)
/workspace/shared/rest/onramp/softwaregateway/schemas.go                                           : 100.00% (1/1 statements)
/workspace/shared/rest/onramp/softwaregateway/softwaregateway.go                                   : 100.00% (199/199 statements)
/workspace/shared/schema_mgmt/mocks.go                                                             : 100.00% (4/4 statements)
/workspace/shared/schema_mgmt/schema_mgmt.go                                                       : 100.00% (227/227 statements)
/workspace/shared/util/rand.go                                                                     : 100.00% (4/4 statements)
/workspace/shared/util/test/test.go                                                                : 100.00% (25/25 statements)
-----
Overall coverage: 91.95% (9931/10801 statements)

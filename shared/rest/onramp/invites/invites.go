package invites

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/mail"
	"regexp"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"
	"github.com/google/uuid"
	"synapse-its.com/shared/api/response"
	security "synapse-its.com/shared/api/security"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/rest/onramp/helper"
	"synapse-its.com/shared/schemas"
)

type HandlerDeps struct {
	GetConnections            func(context.Context, ...bool) (*connect.Connections, error)
	GetBQBatch                func(ctx context.Context) (bqbatch.Batcher, error)
	CreateInvite              func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error)
	GenerateToken             func(length uint) (string, error)
	HashString                func(input string) string
	GetOrganizationName       func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error)
	RenderEmailTemplate       func(data EmailTemplateData) (string, error)
	RenderEmailSubject        func(data EmailTemplateData) (string, error)
	PublishEmailNotification  func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error
	LogInviteEvent            func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error
	GetInvitesForUser         func(pg connect.DatabaseExecutor, userID uuid.UUID) (*[]UserInvite, error)
	GetInvitesForOrganization func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*[]UserInvite, error)
	UpdateInviteStatus        func(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error
	GetInviteByID             func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error)
	UpdateInviteToken         func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error
	ValidateInviteToken       func(pg connect.DatabaseExecutor, token string) (*UserInvite, error)
	CheckCooldownPeriod       func(invite *UserInvite) error
}

const (
	inviteTokenLength = 64
	appName           = "Onramp"
	pubSubTopic       = pubsubdata.TopicETLNotifications

	// Statuses
	StatusPending  = "pending"
	StatusRedeemed = "redeemed"
	StatusRevoked  = "revoked"
	StatusExpired  = "expired"
	StatusRejected = "rejected"

	// Event types
	EventTypeCreate   = "create"
	EventTypeRetry    = "retry"
	EventTypeRevoke   = "revoke"
	EventTypeRedeem   = "redeem"
	EventTypeRejected = "rejected"
)

// Variables to allow dependency injection for testing
var (
	jsonMarshal               = json.Marshal
	pubsubdataBuildAttributes = pubsubdata.BuildAttributes
	timeNow                   = time.Now
	mailParseAddress          = mail.ParseAddress
	regexpMatchString         = regexp.MatchString
)

// CreateInviteHandlerWithDeps creates a new invite with dependency injection
func CreateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get BigQuery batcher
		batcher, err := deps.GetBQBatch(ctx)
		if err != nil {
			logger.Errorf("Error getting BigQuery batcher: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Validate organization ID
		orgID, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("Error validating organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get organization name
		orgName, err := deps.GetOrganizationName(pg, orgID)
		if err != nil {
			logger.Errorf("Error getting organization name: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse and validate request body
		var req CreateInviteRequest
		if err = json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Error decoding request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate required fields
		if err = validateCreateInviteRequest(&req); err != nil {
			logger.Errorf("Error validating request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Create invite token
		token, err := deps.GenerateToken(inviteTokenLength)
		if err != nil {
			logger.Errorf("Error generating invite token: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Hash the token
		tokenHash := deps.HashString(token)

		// Create invite
		invite, err := deps.CreateInvite(pg, orgID, tokenHash, req)
		if err != nil {
			logger.Errorf("Error creating invite: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Add to BigQuery
		if err = deps.LogInviteEvent(batcher, EventTypeCreate, invite, req.InviterID.String()); err != nil {
			logger.Errorf("failed to log invite event: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		// Render email template
		var messageText string
		if req.Message != nil {
			messageText = *req.Message
		}
		emailTemplateData := EmailTemplateData{
			AppName:          appName,
			Message:          messageText,
			InviteLink:       buildInviteLink(orgID.String(), token),
			OrganizationName: orgName,
		}

		htmlContent, err := deps.RenderEmailTemplate(emailTemplateData)
		if err != nil {
			logger.Errorf("failed to render email template: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Render email subject
		subject, err := deps.RenderEmailSubject(emailTemplateData)
		if err != nil {
			logger.Errorf("failed to render email subject: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Publish email notification
		message := pubsubdata.NotificationRequest{
			Type: "email",
			Payload: map[string]interface{}{
				"to":      invite.Email,
				"subject": subject,
				"body":    htmlContent,
			},
			Metadata: map[string]interface{}{
				// Source and processing info
				"source":          "onramp_invites",
				"notification_id": fmt.Sprintf("create-invite-%s", invite.ID),
				"created_at":      time.Now().UTC(),
				"endpoint":        "CreateInviteHandler",
				"action":          "create_invite",

				// Invite information
				"invite_id":     invite.ID,
				"invite_email":  invite.Email,
				"invite_status": invite.Status,
				"invite_type":   "organization_invite",

				// Email content information
				"subject_length": len(subject),
				"body_length":    len(htmlContent),
				"content_type":   "html",

				// Organization context
				"organization_id":   invite.OrganizationIdentifier,
				"organization_name": orgName,
			},
		}
		if err := deps.PublishEmailNotification(ctx, connections.Pubsub, pubSubTopic, message); err != nil {
			logger.Errorf("failed to publish email notification: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(toInviteResponse(invite), w)
	}
}

// ListUserInvitesHandlerWithDeps lists all invites for an organization
func ListUserInvitesForUserHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate user ID
		userID, err := helper.ValidateUserID(r)
		if err != nil {
			logger.Errorf("Error validating user ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get invites from user
		invites, err := deps.GetInvitesForUser(pg, userID)
		if err != nil {
			logger.Errorf("Error getting invites for user: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Convert to invite response
		inviteResponses := toInviteResponseSlice(invites)

		// Return success response
		response.CreateSuccessResponse(inviteResponses, w)
	}
}

// ListUserInvitesForOrganizationHandlerWithDeps lists all invites for an organization
func ListUserInvitesForOrganizationHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID
		orgID, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("Error validating organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate organization exists and is not deleted
		_, err = deps.GetOrganizationName(pg, orgID)
		if err != nil {
			logger.Errorf("Error getting organization name: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get invites from organization
		invites, err := deps.GetInvitesForOrganization(pg, orgID)
		if err != nil {
			logger.Errorf("Error getting invites for organization: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Convert to invite response
		inviteResponses := toInviteResponseSlice(invites)

		// Return success response
		response.CreateSuccessResponse(inviteResponses, w)
	}
}

// RejectInviteHandlerWithDeps rejects an invite
func RejectInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get batcher
		batcher, err := deps.GetBQBatch(ctx)
		if err != nil {
			logger.Errorf("Error getting BigQuery batcher: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Validate user ID
		userID, err := helper.ValidateUserID(r)
		if err != nil {
			logger.Errorf("Error validating user ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate invite ID
		inviteID, err := helper.ValidateInviteID(r)
		if err != nil {
			logger.Errorf("Error validating invite ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get invite by invite ID
		invite, err := deps.GetInviteByID(pg, inviteID)
		if err != nil {
			logger.Errorf("Error getting invite by ID: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Time now
		now := timeNow().UTC()

		// Reject invite
		if err := deps.UpdateInviteStatus(pg, inviteID, StatusRejected, &now); err != nil {
			if err == ErrInviteNotFound {
				logger.Errorf("Invite not found: %v", err)
				response.CreateNotFoundResponse(w)
			} else {
				logger.Errorf("Error rejecting invite: %v", err)
				response.CreateInternalErrorResponse(w)
			}
			return
		}

		// Update invite status for logging
		invite.Status = StatusRejected
		invite.Updated = now

		// Log invite event
		if err := deps.LogInviteEvent(batcher, EventTypeRejected, invite, userID.String()); err != nil {
			logger.Errorf("failed to log invite event: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		// Return success response
		response.CreateSuccessResponse(toInviteResponse(invite), w)
	}
}

// RevokeInviteHandlerWithDeps revokes an invite
func RevokeInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get batcher
		batcher, err := deps.GetBQBatch(ctx)
		if err != nil {
			logger.Errorf("Error getting BigQuery batcher: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Validate organization ID
		orgID, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("Error validating organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate invite ID
		inviteID, err := helper.ValidateInviteID(r)
		if err != nil {
			logger.Errorf("Error validating invite ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get invite by invite ID
		invite, err := deps.GetInviteByID(pg, inviteID)
		if err != nil {
			if err == ErrInviteNotFound {
				logger.Errorf("Invite not found: %v", err)
				response.CreateNotFoundResponse(w)
			} else {
				logger.Errorf("Error getting invite by ID: %v", err)
				response.CreateInternalErrorResponse(w)
			}
			return
		}
		// Parse request body
		req, err := parseRevokeInviteRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Verify invite belongs to the organization
		if invite.OrganizationIdentifier != orgID {
			logger.Errorf("Invite does not belong to the organization: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Time now
		now := timeNow().UTC()

		// Revoke invite
		if err := deps.UpdateInviteStatus(pg, inviteID, StatusRevoked, &now); err != nil {
			logger.Errorf("Error revoking invite: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Update invite status for logging
		invite.Status = StatusRevoked
		invite.Updated = now

		// Log invite event
		if err := deps.LogInviteEvent(batcher, EventTypeRevoke, invite, req.Actor); err != nil {
			logger.Errorf("failed to log invite event: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(toInviteResponse(invite), w)
	}
}

// ResendInviteHandlerWithDeps resends an invite
func ResendInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get batcher
		batcher, err := deps.GetBQBatch(ctx)
		if err != nil {
			logger.Errorf("Error getting BigQuery batcher: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Validate organization ID
		orgID, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("Error validating organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate invite ID
		inviteID, err := helper.ValidateInviteID(r)
		if err != nil {
			logger.Errorf("Error validating invite ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		req, err := parseResendInviteRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get organization name
		orgName, err := deps.GetOrganizationName(pg, orgID)
		if err != nil {
			logger.Errorf("Error getting organization name: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get invite by invite ID
		invite, err := deps.GetInviteByID(pg, inviteID)
		if err != nil {
			if err == ErrInviteNotFound {
				logger.Errorf("Invite not found: %v", err)
				response.CreateNotFoundResponse(w)
			} else {
				logger.Errorf("Error getting invite by ID: %v", err)
				response.CreateInternalErrorResponse(w)
			}
			return
		}

		// Verify invite belongs to the organization
		if invite.OrganizationIdentifier != orgID {
			logger.Errorf("Invite does not belong to the organization: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Check cooldown period
		if err = deps.CheckCooldownPeriod(invite); err != nil {
			if err == ErrResendCooldown {
				logger.Errorf("Resend cooldown period not passed: %v", err)
				response.CreateCustomErrorResponse("Must wait 1 minute before resending", nil, http.StatusTooEarly, w)
			} else {
				logger.Errorf("Error checking cooldown period: %v", err)
				response.CreateInternalErrorResponse(w)
			}
			return
		}

		// Create invite token
		token, err := deps.GenerateToken(inviteTokenLength)
		if err != nil {
			logger.Errorf("Error generating invite token: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Hash the token
		tokenHash := deps.HashString(token)

		// Update invite status for logging
		invite.TokenHash = tokenHash
		invite.RetryCount++

		// Update invite with new token
		if err = deps.UpdateInviteToken(pg, inviteID, tokenHash, invite.RetryCount, req); err != nil {
			logger.Errorf("Error updating invite token: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Log invite event
		if err = deps.LogInviteEvent(batcher, EventTypeRetry, invite, req.Actor); err != nil {
			logger.Errorf("failed to log invite event: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Render email template
		messageText := ""
		if req.Message != nil {
			messageText = *req.Message
		}

		emailTemplateData := EmailTemplateData{
			AppName:          appName,
			Message:          messageText,
			InviteLink:       buildInviteLink(orgID.String(), token),
			OrganizationName: orgName,
		}

		htmlContent, err := deps.RenderEmailTemplate(emailTemplateData)
		if err != nil {
			logger.Errorf("failed to render email template: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Render email subject
		subject, err := deps.RenderEmailSubject(emailTemplateData)
		if err != nil {
			logger.Errorf("failed to render email subject: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Publish email notification
		message := pubsubdata.NotificationRequest{
			Type: "email",
			Payload: map[string]interface{}{
				"to":      invite.Email,
				"subject": subject,
				"body":    htmlContent,
			},
			Metadata: map[string]interface{}{
				// Source and processing info
				"source":          "onramp_invites",
				"notification_id": fmt.Sprintf("resend-invite-%s", invite.ID),
				"created_at":      time.Now().UTC(),
				"endpoint":        "ResendInviteHandler",
				"action":          "resend_invite",

				// Invite information
				"invite_id":     invite.ID,
				"invite_email":  invite.Email,
				"invite_status": invite.Status,
				"invite_type":   "organization_invite",

				// Email content information
				"subject_length": len(subject),
				"body_length":    len(htmlContent),
				"content_type":   "html",

				// Organization context
				"organization_id":   invite.OrganizationIdentifier,
				"organization_name": orgName,

				// Resend specific metadata
				"original_created_at": invite.Created,
				"resend_actor":        req.Actor,
			},
		}
		if err := deps.PublishEmailNotification(ctx, connections.Pubsub, pubSubTopic, message); err != nil {
			logger.Errorf("failed to publish email notification: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		responseData := map[string]interface{}{
			"id":     invite.ID,
			"email":  invite.Email,
			"status": invite.Status,
		}

		// Return success response
		response.CreateSuccessResponse(responseData, w)
	}
}

// ValidateInviteHandlerWithDeps validates an invite
func ValidateInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate Organization ID
		orgID, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("Error validating organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get token from query parameters
		token := r.URL.Query().Get("token")
		if token == "" {
			logger.Errorf("Error getting token from query parameters: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate token
		invite, err := deps.ValidateInviteToken(pg, token)
		if err != nil {
			logger.Errorf("Error validating invite token: %v", err)
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Validate organization ID with invite
		if invite.OrganizationIdentifier != orgID {
			logger.Errorf("Organization ID does not match invite: %v", err)
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(toInviteResponse(invite), w)
	}
}

// RedeemInviteHandlerWithDeps redeems an invite
func RedeemInviteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the connections
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get batcher
		batcher, err := deps.GetBQBatch(ctx)
		if err != nil {
			logger.Errorf("Error getting BigQuery batcher: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Validate user ID
		userID, err := helper.ValidateUserID(r)
		if err != nil {
			logger.Errorf("Error validating user ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate invite ID
		inviteID, err := helper.ValidateInviteID(r)
		if err != nil {
			logger.Errorf("Error validating invite ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get invite by invite ID
		invite, err := deps.GetInviteByID(pg, inviteID)
		if err != nil {
			if err == ErrInviteNotFound {
				logger.Errorf("Invite not found: %v", err)
				response.CreateNotFoundResponse(w)
			} else {
				logger.Errorf("Error getting invite by ID: %v", err)
				response.CreateInternalErrorResponse(w)
			}
			return
		}

		// Verify invite is in pending status
		if invite.Status != StatusPending {
			response.CreateBadRequestResponse(w)
			return
		}

		// Time now
		now := time.Now().UTC()

		// Update invite status to redeemed
		if err := deps.UpdateInviteStatus(pg, inviteID, StatusRedeemed, &now); err != nil {
			logger.Errorf("Error updating invite status: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Update invite status for logging
		invite.Status = StatusRedeemed
		invite.Updated = now

		// Log invite event
		if err := deps.LogInviteEvent(batcher, EventTypeRedeem, invite, userID.String()); err != nil {
			logger.Errorf("failed to log invite event: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(toInviteResponse(invite), w)
	}
}

// validateInviteToken validates an invite token
func validateInviteToken(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
	// Validate token format
	if !validateTokenFormat(token) {
		return nil, ErrInvalidInviteToken
	}

	tokenHash := security.CalculateSHA256(token)

	query := `
		SELECT
			id,
			organizationidentifier,
			tokenhash,
			email,
			inviterid,
			customroleid,
			status,
			message,
			requiresso,
			retrycount,
			retried,
			expired,
			created,
			sent,
			updated
		FROM {{UserInvites}}
		WHERE tokenhash = $1
	`
	invite := &UserInvite{}
	err := pg.QueryRowStruct(invite, query, tokenHash)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrInviteNotFound
		}
		logger.Errorf("failed to validate invite token: %v", err)
		return nil, ErrInviteNotFound
	}

	// Check if the invite is expired
	if invite.Expired != nil && invite.Expired.Before(timeNow().UTC()) {
		return nil, ErrInviteExpired
	}

	return invite, nil
}

// updateInviteToken updates the invite token
func updateInviteToken(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
	// Time now
	now := timeNow().UTC()

	// Parse expired days
	var expired *time.Time
	if req.ExpiredDays != nil && *req.ExpiredDays > 0 {
		exp := timeNow().UTC().AddDate(0, 0, *req.ExpiredDays)
		expired = &exp
	} else {
		expired = nil
	}

	// Parse message
	var message *string
	if req.Message != nil {
		message = req.Message
	}

	// Build query
	query := `
		UPDATE {{UserInvites}}
		SET 
			tokenhash = $1,
			retrycount = $2,
			retried = $3,
			updated = $3,
			expired = $4,
			message = $5
		WHERE id = $6
	`

	// Execute query
	result, err := pg.Exec(query, tokenHash, retryCount, now, expired, message, inviteID)
	if err != nil {
		logger.Errorf("failed to update invite token: %v", err)
		return ErrDatabaseOperation
	}

	// Check if the update was successful
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("failed to get rows affected: %v", err)
		return ErrDatabaseOperation
	}

	if rowsAffected == 0 {
		return ErrInviteNotFound
	}

	return nil
}

// createInvite creates a new invite
func createInvite(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
	// Time now
	now := timeNow().UTC()

	// Parse expired days
	var expired *time.Time
	if req.ExpiredDays != nil && *req.ExpiredDays > 0 {
		exp := timeNow().UTC().AddDate(0, 0, *req.ExpiredDays)
		expired = &exp
	} else {
		expired = nil
	}

	// Parse message
	var message *string
	if req.Message != nil {
		message = req.Message
	}

	// Build query
	query := `
		INSERT INTO {{UserInvites}} (
				organizationidentifier,
				tokenhash,
				email,
				inviterid,
				customroleid,
				status,
				message,
				requiresso,
				retrycount,
				retried,
				expired,
				created,
				sent,
				updated
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
			)
			RETURNING id`

	// Execute query
	row, err := pg.QueryRow(
		query,
		orgID,
		tokenHash,
		req.Email,
		req.InviterID,
		req.OrganizationRole,
		StatusPending,
		message,
		false,
		0,   // retrycount
		nil, // retried
		expired,
		now,
		nil, // sent
		now,
	)
	if err != nil {
		logger.Errorf("failed to create invite: %v", err)
		return nil, ErrDatabaseOperation
	}

	// Scan the result
	inviteIDInterface, ok := row["id"]
	if !ok {
		return nil, ErrInvalidInviteID
	}

	// Convert string to UUID
	inviteIDStr, ok := inviteIDInterface.(string)
	if !ok {
		return nil, fmt.Errorf("ID column is not a string: %T", inviteIDInterface)
	}

	inviteID, err := uuid.Parse(inviteIDStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse invite ID as UUID: %w", err)
	}

	// Create invite
	invite := &UserInvite{
		ID:                     inviteID,
		OrganizationIdentifier: orgID,
		TokenHash:              tokenHash,
		Email:                  req.Email,
		InviterID:              req.InviterID,
		CustomRoleID:           req.OrganizationRole,
		Status:                 StatusPending,
		Message:                message,
		RequireSSO:             false,
		RetryCount:             0,
		Retried:                nil,
		Expired:                expired,
		Created:                now,
		Sent:                   nil,
		Updated:                now,
	}
	return invite, nil
}

// EmailResult represents an email result from database query
type EmailResult struct {
	Email string `json:"email"`
}

// getInvitesForUser gets all invites for a user
func getInvitesForUser(pg connect.DatabaseExecutor, userID uuid.UUID) (*[]UserInvite, error) {
	// Get all email addresses for user from AuthMethod
	emailQuery := `SELECT email FROM {{AuthMethod}} WHERE id = $1 AND isdeleted = false AND email IS NOT NULL`

	var emailResults []EmailResult
	err := pg.QueryGenericSlice(&emailResults, emailQuery, userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNoUserEmail
		}
		logger.Errorf("failed to get emails for user: %v", err)
		return nil, ErrDatabaseOperation
	}

	// If no emails found, return error
	if len(emailResults) == 0 {
		return nil, ErrNoUserEmail
	}

	// Extract email addresses into a slice
	emails := make([]interface{}, len(emailResults))
	for i, result := range emailResults {
		emails[i] = result.Email
	}

	// Build the IN clause with the correct number of placeholders
	placeholders := ""
	for i := 0; i < len(emails); i++ {
		if i > 0 {
			placeholders += ", "
		}
		placeholders += fmt.Sprintf("$%d", i+1)
	}

	query := fmt.Sprintf(`
		SELECT
			id,
			organizationidentifier,
			tokenhash,
			email,
			inviterid,
			customroleid,
			status,
			message,
			requiresso,
			retrycount,
			retried,
			expired,
			created,
			sent,
			updated
		FROM {{UserInvites}}
		WHERE email IN (%s)
	`, placeholders)

	var invites []UserInvite
	err = pg.QueryGenericSlice(&invites, query, emails...)
	if err != nil {
		if err == sql.ErrNoRows {
			// No invites found is not an error - return empty slice
			return &[]UserInvite{}, nil
		}
		logger.Errorf("failed to get invites for user emails: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &invites, nil
}

// getInvitesForOrganization gets all invites for an organization
func getInvitesForOrganization(pg connect.DatabaseExecutor, orgID uuid.UUID) (*[]UserInvite, error) {
	query := `
		SELECT
			id,
			organizationidentifier,
			tokenhash,
			email,
			inviterid,
			customroleid,
			status,
			message,
			requiresso,
			retrycount,
			retried,
			expired,
			created,
			sent,
			updated
		FROM {{UserInvites}}
		WHERE organizationidentifier = $1
	`
	var invites []UserInvite
	err := pg.QueryGenericSlice(&invites, query, orgID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNoInvitesFoundForOrganization
		}
		logger.Errorf("failed to get invites from organization: %v", err)
		return nil, ErrDatabaseOperation
	}
	return &invites, nil
}

// updateInviteStatus updates the status of an invite
func updateInviteStatus(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error {
	query := `
		UPDATE {{UserInvites}}
		SET status = $1, updated = $2
		WHERE id = $3
	`
	result, err := pg.Exec(query, status, now.UTC(), inviteID)
	if err != nil {
		logger.Errorf("failed to update invite status: %v", err)
		return ErrDatabaseOperation
	}

	// Check if the update was successful
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("failed to get rows affected: %v", err)
		return ErrDatabaseOperation
	}
	if rowsAffected == 0 {
		return ErrInviteNotFound
	}

	return nil
}

// getInviteByID gets an invite by invite ID
func getInviteByID(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
	query := `
		SELECT
			id,
			organizationidentifier,
			tokenhash,
			email,
			inviterid,
			customroleid,
			status,
			message,
			requiresso,
			retrycount,
			retried,
			expired,
			created,
			sent,
			updated
		FROM {{UserInvites}}
		WHERE id = $1
	`
	invite := &UserInvite{}
	err := pg.QueryRowStruct(invite, query, inviteID)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, ErrInviteNotFound
	}
	if err != nil {
		return nil, ErrDatabaseOperation
	}
	return invite, nil
}

// Convert to invite response
func toInviteResponse(invite *UserInvite) *InviteResponse {
	return &InviteResponse{
		ID:                     invite.ID,
		OrganizationIdentifier: invite.OrganizationIdentifier,
		Email:                  invite.Email,
		InviterID:              invite.InviterID,
		CustomRoleID:           invite.CustomRoleID,
		Status:                 invite.Status,
		Message:                invite.Message,
		RequireSSO:             invite.RequireSSO,
		RetryCount:             invite.RetryCount,
		Retried:                invite.Retried,
		Expired:                invite.Expired,
		Created:                invite.Created,
		Sent:                   invite.Sent,
		Updated:                invite.Updated,
	}
}

// validateCreateInviteRequest validates the create invite request
func validateCreateInviteRequest(req *CreateInviteRequest) error {
	// Validate email
	if req.Email == "" {
		return ErrInvalidEmail
	}

	if _, err := mailParseAddress(req.Email); err != nil {
		return ErrInvalidEmail
	}

	// Validate inviter ID
	if req.InviterID == uuid.Nil {
		return ErrInvalidUserID
	}

	// Validate organization role
	if req.OrganizationRole == uuid.Nil {
		return ErrInvalidRequestBody
	}

	return nil
}

// buildInviteLink constructs the invite validation link
var buildInviteLink = func(orgID, token string) string {
	return fmt.Sprintf("/api/organizations/%s/invites/validate?token=%s", orgID, token)
}

// validateTokenFormat checks if a token has the correct format (64 hex characters)
var validateTokenFormat = func(token string) bool {
	if len(token) != 64 {
		return false
	}

	// Check if it's valid hexadecimal
	matched, _ := regexpMatchString("^[a-fA-F0-9]{64}$", token)
	return matched
}

// Convert to invite response slice
var toInviteResponseSlice = func(invites *[]UserInvite) *[]InviteResponse {
	inviteResponses := make([]InviteResponse, len(*invites))
	for i, invite := range *invites {
		inviteResponses[i] = *toInviteResponse(&invite)
	}
	return &inviteResponses
}

// checkCooldownPeriod checks if the cooldown period has passed
var checkCooldownPeriod = func(invite *UserInvite) error {
	// Check if the invite is nil (defensive programming)
	if invite == nil {
		logger.Error("Invite is nil after successful retrieval")
		return ErrInvalidInviteID
	}

	// Check if the invite has been sent
	if invite.Retried == nil {
		return nil
	}

	// Set cooldown period (1 minute)
	cooldownPeriod := time.Minute
	timeSinceRetry := timeNow().UTC().Sub(*invite.Retried)

	// Check if the cooldown period has passed
	if timeSinceRetry < cooldownPeriod {
		return ErrResendCooldown
	}

	return nil
}

// getOrganizationName retrieves the organization name from the database
var getOrganizationName = func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
	query := `SELECT name FROM {{Organization}} WHERE id = $1 AND isdeleted = false`

	row, err := pg.QueryRow(query, orgID)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", ErrOrganizationNotFound
		}
		logger.Errorf("failed to get organization name: %v", err)
		return "", fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	nameInterface, ok := row["name"]
	if !ok {
		return "", ErrInvalidRowData
	}

	name, ok := nameInterface.(string)
	if !ok {
		return "", ErrInvalidRowData
	}

	return name, nil
}

// logInviteEvent logs an invite event to BigQuery
var logInviteEvent = func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
	// Convert *string to bigquery.NullString
	var message bigquery.NullString
	if invite.Message != nil {
		message.StringVal = *invite.Message
		message.Valid = true
	}

	// Handle nil time pointers by providing zero values
	var retriedTime, expiredTime, sentTime time.Time
	if invite.Retried != nil {
		retriedTime = *invite.Retried
	}
	if invite.Expired != nil {
		expiredTime = *invite.Expired
	}
	if invite.Sent != nil {
		sentTime = *invite.Sent
	}

	event := &schemas.InviteEvent{
		UserInviteID:           invite.ID.String(),
		EventType:              eventType,
		Actor:                  actor,
		EventTime:              timeNow().UTC(),
		OrganizationIdentifier: invite.OrganizationIdentifier.String(),
		TokenHash:              invite.TokenHash,
		Email:                  invite.Email,
		InviterID:              invite.InviterID.String(),
		OrganizationRole:       invite.CustomRoleID.String(),
		Status:                 invite.Status,
		Message:                message,
		RequireSSO:             invite.RequireSSO,
		RetryCount:             int64(invite.RetryCount),
		Retried:                retriedTime,
		Expired:                expiredTime,
		Created:                invite.Created,
		Sent:                   sentTime,
		Updated:                invite.Updated,
	}

	if err := batcher.Add(event); err != nil {
		logger.Errorf("failed to log invite event: %v", err)
		return ErrBigQueryOperation
	}

	return nil
}

// publishEmailNotification publishes an email notification to PubSub
var publishEmailNotification = func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
	// Marshal the message
	jsonData, err := jsonMarshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal email message: %v", err)
	}

	// Build attributes
	attributes := pubsubdataBuildAttributes(
		pubsubdata.CommonAttributes{
			Topic: topicName,
		},
		pubsubdata.HeaderDetails{})

	topic := pubsubClient.Topic(pubSubTopic)
	pubsubMsg := &pubsub.Message{
		Data:       jsonData,
		Attributes: attributes,
	}
	result := topic.Publish(ctx, pubsubMsg)
	if _, err := result.Get(ctx); err != nil {
		return fmt.Errorf("failed to publish email message: %v", err)
	}

	logger.Infof("Published email notification to PubSub: %s", string(jsonData))

	return nil
}

// parseRevokeInviteRequest parses and validates the revoke invite request body
func parseRevokeInviteRequest(r *http.Request) (*RevokeInviteRequest, error) {
	var req RevokeInviteRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		return nil, fmt.Errorf("error decoding request body: %w", err)
	}

	if req.Actor == "" {
		return nil, errors.New("actor is required")
	}

	return &req, nil
}

// parseResendInviteRequest parses and validates the resend invite request body
func parseResendInviteRequest(r *http.Request) (*ResendInviteRequest, error) {
	var req ResendInviteRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		return nil, fmt.Errorf("error decoding request body: %w", err)
	}

	if req.Actor == "" {
		return nil, errors.New("actor is required")
	}

	return &req, nil
}

var CreateInviteHandler = CreateInviteHandlerWithDeps(HandlerDeps{
	GetConnections:           connect.GetConnections,
	GetBQBatch:               bqbatch.GetBatch,
	CreateInvite:             createInvite,
	GenerateToken:            helper.GenerateRandomTokenHex,
	HashString:               security.CalculateSHA256,
	GetOrganizationName:      getOrganizationName,
	RenderEmailTemplate:      renderEmailTemplate,
	RenderEmailSubject:       renderEmailSubject,
	LogInviteEvent:           logInviteEvent,
	PublishEmailNotification: publishEmailNotification,
})

var ListUserInvitesForUserHandler = ListUserInvitesForUserHandlerWithDeps(HandlerDeps{
	GetConnections:    connect.GetConnections,
	GetInvitesForUser: getInvitesForUser,
})

var ListUserInvitesForOrganizationHandler = ListUserInvitesForOrganizationHandlerWithDeps(HandlerDeps{
	GetConnections:            connect.GetConnections,
	GetInvitesForOrganization: getInvitesForOrganization,
	GetOrganizationName:       getOrganizationName,
})

var RejectInviteHandler = RejectInviteHandlerWithDeps(HandlerDeps{
	GetConnections:     connect.GetConnections,
	GetBQBatch:         bqbatch.GetBatch,
	UpdateInviteStatus: updateInviteStatus,
	GetInviteByID:      getInviteByID,
	LogInviteEvent:     logInviteEvent,
})

var RevokeInviteHandler = RevokeInviteHandlerWithDeps(HandlerDeps{
	GetConnections:     connect.GetConnections,
	GetBQBatch:         bqbatch.GetBatch,
	UpdateInviteStatus: updateInviteStatus,
	GetInviteByID:      getInviteByID,
	LogInviteEvent:     logInviteEvent,
})

var ResendInviteHandler = ResendInviteHandlerWithDeps(HandlerDeps{
	GetConnections:           connect.GetConnections,
	GetBQBatch:               bqbatch.GetBatch,
	GetInviteByID:            getInviteByID,
	UpdateInviteToken:        updateInviteToken,
	GenerateToken:            helper.GenerateRandomTokenHex,
	HashString:               security.CalculateSHA256,
	GetOrganizationName:      getOrganizationName,
	RenderEmailTemplate:      renderEmailTemplate,
	RenderEmailSubject:       renderEmailSubject,
	LogInviteEvent:           logInviteEvent,
	PublishEmailNotification: publishEmailNotification,
	CheckCooldownPeriod:      checkCooldownPeriod,
})

var ValidateInviteHandler = ValidateInviteHandlerWithDeps(HandlerDeps{
	GetConnections:      connect.GetConnections,
	ValidateInviteToken: validateInviteToken,
})

var RedeemInviteHandler = RedeemInviteHandlerWithDeps(HandlerDeps{
	GetConnections:     connect.GetConnections,
	GetBQBatch:         bqbatch.GetBatch,
	UpdateInviteStatus: updateInviteStatus,
	GetInviteByID:      getInviteByID,
	LogInviteEvent:     logInviteEvent,
})
